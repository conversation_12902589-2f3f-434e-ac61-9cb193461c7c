import { useState } from 'react';

function Home() {
  const [message, setMessage] = useState('');
  const [showInfo, setShowInfo] = useState(false);

  const handleStart = () => {
    setMessage('🚀 تم الضغط على "ابدأ الآن"! مرحباً بك في رحلة التطوير');
    setShowInfo(false);
  };

  const handleLearnMore = () => {
    setShowInfo(!showInfo);
    setMessage('');
  };

  return (
    <div className="w-screen h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="text-center space-y-6 p-8 max-w-2xl">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">
          مرحباً بك في التطبيق! 🎉
        </h1>
        <p className="text-lg text-gray-600 mb-6">
          تم تشغيل التطبيق بنجاح باستخدام React + TypeScript + Vite
        </p>

        {message && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-4">
            {message}
          </div>
        )}

        {showInfo && (
          <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded-lg mb-4">
            <h3 className="font-bold mb-2">معلومات التطبيق:</h3>
            <ul className="text-sm text-right space-y-1">
              <li>• React 18 مع TypeScript</li>
              <li>• Vite للتطوير السريع</li>
              <li>• Tailwind CSS للتصميم</li>
              <li>• مكونات UI جاهزة من Radix UI</li>
              <li>• دعم React Router للتنقل</li>
            </ul>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={handleStart}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors transform hover:scale-105"
          >
            ابدأ الآن
          </button>
          <button
            onClick={handleLearnMore}
            className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors transform hover:scale-105"
          >
            {showInfo ? 'إخفاء المعلومات' : 'تعرف على المزيد'}
          </button>
        </div>

        <div className="mt-8 text-sm text-gray-500">
          <p>التطبيق يعمل على المنفذ: localhost:5174</p>
        </div>
      </div>
    </div>
  )
}

export default Home
