function Home() {
  return (
    <div className="w-screen h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="text-center space-y-6 p-8">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">
          مرحباً بك في التطبيق! 🎉
        </h1>
        <p className="text-lg text-gray-600 mb-6">
          تم تشغيل التطبيق بنجاح باستخدام React + TypeScript + Vite
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            ابدأ الآن
          </button>
          <button className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
            تعرف على المزيد
          </button>
        </div>
        <div className="mt-8 text-sm text-gray-500">
          <p>التطبيق يعمل على المنفذ: localhost:5174</p>
        </div>
      </div>
    </div>
  )
}

export default Home
